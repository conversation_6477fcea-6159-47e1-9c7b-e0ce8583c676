const fs = require('fs').promises;

// Load seller data files
async function loadSellerData() {
  const sellers = ['chef', 'sham', 'sysco', 'usfood', 'greco', 'depot', 'perf'];
  const sellerData = {};

  for (const seller of sellers) {
    try {
      const data = await fs.readFile(`${seller}.json`, 'utf8');
      const products = JSON.parse(data);

      // Create lookup by product number
      sellerData[seller] = {};
      products.forEach(product => {
        if (product.productnumber) {
          sellerData[seller][product.productnumber] = product;
        }
      });

      console.log(`Loaded ${Object.keys(sellerData[seller]).length} products from ${seller}.json`);
    } catch (error) {
      console.error(`Error loading ${seller}.json:`, error.message);
      sellerData[seller] = {};
    }
  }

  return sellerData;
}

// Extract price properties from a product
function extractPriceProperties(product) {
  const priceProps = {};
  const portionPriceProps = {};

  for (const [key, value] of Object.entries(product)) {
    if (typeof value === 'string' || typeof value === 'number') {
      const keyLower = key.toLowerCase();

      if (keyLower.includes('price')) {
        if (keyLower.includes('portion')) {
          portionPriceProps[key] = parseFloat(value) || 0;
        } else {
          priceProps[key] = parseFloat(value) || 0;
        }
      }
    }
  }

  return { priceProps, portionPriceProps };
}

// Apply price calculation rules from memory
function calculatePrice(product, seller) {
  let finalPrice = 0;
  let finalPortionPrice = 0;

  const quantity = parseFloat(product.quantity) || 1;
  const unittype = product.unittype || '';
  const priceunittype = product.priceunittype || unittype;

  // For chef supplier, return multiple price options for comparison
  if (seller === 'chef') {
    return calculateChefPrices(product, quantity, unittype);
  }

  // For sham supplier, handle dual price sets
  if (seller === 'sham') {
    return calculateShamPrices(product, quantity, unittype);
  }

  if (seller === 'sysco') {
    // Sysco price calculation rules
    const caseprice = product.caseprice || '';
    const casepriceNumeric = parseFloat(product.price) || 0;

    if (caseprice && !caseprice.includes('EA') && !caseprice.includes('CS')) {
      finalPortionPrice = casepriceNumeric;
      finalPrice = casepriceNumeric * quantity;
    } else if (caseprice && (caseprice.includes('EA') || caseprice.includes('CS'))) {
      finalPrice = casepriceNumeric;
      finalPortionPrice = quantity > 0 ? casepriceNumeric / quantity : 0;
    } else {
      finalPrice = casepriceNumeric;
      finalPortionPrice = parseFloat(product.portionprice) || 0;
    }
  } else if (seller === 'usfood' || seller === 'sham') {
    // Remove duplicate productNumber property and filter empty prices
    finalPrice = parseFloat(product.price) || 0;
    finalPortionPrice = parseFloat(product.portionprice) || 0;

    if (finalPrice === 0) {
      return null; // Filter out products with empty price values
    }
  } else {
    // Standard calculation for other sellers (chef, greco, depot, perf)
    finalPrice = parseFloat(product.price) || 0;
    finalPortionPrice = parseFloat(product.portionprice) || 0;

    // Special handling for chef data structure
    if (seller === 'chef') {
      const unitPrice = parseFloat(product.unitprice) || 0;
      const casePrice = parseFloat(product.caseprice) || 0;
      const unitPortionPrice = parseFloat(product.unitportionprice) || 0;
      const casePortionPrice = parseFloat(product.caseportionprice) || 0;
      const unitQuantity = parseFloat(product.unitquantity) || 1;
      const caseQuantity = parseFloat(product.casequantity) || unitQuantity;

      // Prefer case pricing if available, otherwise use unit pricing
      if (casePrice > 0 && caseQuantity > 0) {
        finalPrice = casePrice;
        finalPortionPrice = casePortionPrice > 0 ? casePortionPrice : (casePrice / caseQuantity);
      } else if (unitPrice > 0 && unitQuantity > 0) {
        finalPrice = unitPrice;
        finalPortionPrice = unitPortionPrice > 0 ? unitPortionPrice : (unitPrice / unitQuantity);
      }
    }

    // If no price data available, try to extract from other price fields
    if (finalPrice === 0 && finalPortionPrice === 0) {
      // Try to find any price-related fields
      for (const [key, value] of Object.entries(product)) {
        const keyLower = key.toLowerCase();
        const numValue = parseFloat(value);
        if (keyLower.includes('price') && !isNaN(numValue) && numValue > 0) {
          if (keyLower.includes('portion')) {
            finalPortionPrice = numValue;
          } else if (keyLower.includes('unit') && !keyLower.includes('portion')) {
            finalPrice = numValue;
            finalPortionPrice = quantity > 0 ? numValue / quantity : numValue;
          } else if (keyLower.includes('case')) {
            finalPrice = numValue;
            finalPortionPrice = quantity > 0 ? numValue / quantity : numValue;
          }
        }
      }
    }

    // Handle unit type conversions if needed
    if (unittype !== priceunittype && quantity > 0 && finalPortionPrice > 0) {
      // Convert quantity to match priceunittype (e.g., 60 oz becomes 3.75 lb)
      let convertedQuantity = quantity;
      if (unittype === 'oz' && priceunittype === 'lb') {
        convertedQuantity = quantity / 16;
      } else if (unittype === 'lb' && priceunittype === 'oz') {
        convertedQuantity = quantity * 16;
      }

      finalPrice = finalPortionPrice * convertedQuantity;
    }

    // If we still don't have a final price but have portion price, calculate it
    if (finalPrice === 0 && finalPortionPrice > 0 && quantity > 0) {
      finalPrice = finalPortionPrice * quantity;
    }
  }

  // Return null if no valid price data found
  if (finalPrice === 0 && finalPortionPrice === 0) {
    return null;
  }

  return {
    price: finalPrice,
    portionPrice: finalPortionPrice,
    unittype: unittype,
    quantity: quantity
  };
}

// Calculate Chef supplier prices with multiple options
function calculateChefPrices(product, quantity, unittype) {
  const unitPrice = parseFloat(product.unitprice) || 0;
  const casePrice = parseFloat(product.caseprice) || 0;
  const unitPortionPrice = parseFloat(product.unitportionprice) || 0;
  const casePortionPrice = parseFloat(product.caseportionprice) || 0;
  const unitQuantity = parseFloat(product.unitquantity) || 1;
  const caseQuantity = parseFloat(product.casequantity) || unitQuantity;

  const priceOptions = [];

  // Unit pricing option
  if (unitPrice > 0) {
    priceOptions.push({
      price: unitPrice,
      portionPrice: unitPortionPrice > 0 ? unitPortionPrice : (unitPrice / unitQuantity),
      unittype: unittype,
      quantity: unitQuantity,
      priceType: 'unit'
    });
  }

  // Case pricing option
  if (casePrice > 0) {
    priceOptions.push({
      price: casePrice,
      portionPrice: casePortionPrice > 0 ? casePortionPrice : (casePrice / caseQuantity),
      unittype: unittype,
      quantity: caseQuantity,
      priceType: 'case'
    });
  }

  return priceOptions.length > 0 ? { multipleOptions: priceOptions } : null;
}

// Calculate Sham supplier prices with dual price sets
function calculateShamPrices(product, quantity, unittype) {
  const price = parseFloat(product.price) || 0;
  const portionPrice = parseFloat(product.portionprice) || 0;
  const shamPortionPrice = parseFloat(product.shamportionprice) || 0;
  const shamUnitType = product.shamunittype || unittype;

  if (price === 0) {
    return null; // Filter out products with empty price values
  }

  const priceOptions = [];

  // Standard price option
  if (portionPrice > 0) {
    priceOptions.push({
      price: price,
      portionPrice: portionPrice,
      unittype: unittype,
      quantity: quantity,
      priceType: 'standard'
    });
  }

  // Sham price option
  if (shamPortionPrice > 0) {
    priceOptions.push({
      price: price,
      portionPrice: shamPortionPrice,
      unittype: shamUnitType,
      quantity: quantity,
      priceType: 'sham'
    });
  }

  return priceOptions.length > 0 ? { multipleOptions: priceOptions } : null;
}

// Statistical outlier detection using simple clustering
function removeOutliers(prices) {
  if (prices.length <= 2) return prices;

  // Sort prices
  const sortedPrices = [...prices].sort((a, b) => a - b);
  const q1Index = Math.floor(sortedPrices.length * 0.25);
  const q3Index = Math.floor(sortedPrices.length * 0.75);
  const q1 = sortedPrices[q1Index];
  const q3 = sortedPrices[q3Index];
  const iqr = q3 - q1;

  // Define outlier bounds (1.5 * IQR method)
  const lowerBound = q1 - 1.5 * iqr;
  const upperBound = q3 + 1.5 * iqr;

  // Filter out outliers
  return prices.filter(price => price >= lowerBound && price <= upperBound);
}

// Calculate percentage difference between two prices
function calculatePriceDifference(price1, price2) {
  if (price1 === 0 || price2 === 0 || !isFinite(price1) || !isFinite(price2)) return 100;
  const minPrice = Math.min(price1, price2);
  if (minPrice === 0) return 100;

  const difference = Math.abs(price1 - price2) / minPrice * 100;

  // Cap extreme differences at 1000% to prevent overflow
  return Math.min(difference, 1000);
}

// Parse seller and product number from match key
function parseMatchKey(key) {
  const parts = key.split('_');
  if (parts.length >= 2) {
    const seller = parts[0];
    let productNumber = parts.slice(1).join('_');

    // Handle usfood special case
    if (seller === 'usfood' && productNumber.startsWith('#')) {
      productNumber = productNumber.substring(1);
    }

    return { seller, productNumber };
  }
  return null;
}

module.exports = {
  loadSellerData,
  extractPriceProperties,
  calculatePrice,
  calculatePriceDifference,
  parseMatchKey,
  removeOutliers
};

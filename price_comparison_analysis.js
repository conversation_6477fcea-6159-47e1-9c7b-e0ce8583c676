const fs = require('fs').promises;
const {
  loadSellerData,
  extractPriceProperties,
  calculatePrice,
  calculateAllPrices,
  calculatePriceDifference,
  parseMatchKey,
  removeOutliers
} = require('./price_analysis');

// Enhanced price analysis with comprehensive multi-price handling
function analyzeGroupPrices(validProducts) {
  // Step 1: Extract all available prices from each product
  const allProductPrices = [];

  validProducts.forEach(product => {
    const allPrices = product.priceAnalysis.allPrices;
    if (allPrices && allPrices.priceOptions) {
      allPrices.priceOptions.forEach(option => {
        allProductPrices.push({
          matchKey: product.originalProductData.matchKey,
          product: product,
          priceOption: option,
          unittype: option.unittype,
          portionPrice: option.portionPrice,
          regularPrice: option.price,
          priceProperty: option.priceProperty,
          priceType: option.priceType
        });
      });
    }
  });

  if (allProductPrices.length === 0) {
    return {
      productResults: [],
      medianPrice: 0,
      maxVariance: 0,
      groupStatus: 'not close',
      cheapestProductKey: null,
      outlierCount: 0,
      comparisonMethod: 'none'
    };
  }

  // Step 2: Sort and cluster prices to identify outliers
  const portionPrices = allProductPrices
    .filter(p => p.portionPrice > 0)
    .map(p => p.portionPrice);
  const regularPrices = allProductPrices
    .filter(p => p.regularPrice > 0)
    .map(p => p.regularPrice);

  const cleanedPortionPrices = removeOutliers(portionPrices);
  const cleanedRegularPrices = removeOutliers(regularPrices);

  // Step 3: Primary comparison method - matching unit types with portion prices
  let comparisonMethod = 'none';
  let selectedPrices = [];
  let medianPrice = 0;

  // Group by unit type for portion price comparison
  const unitTypeGroups = {};
  allProductPrices.forEach(p => {
    if (p.portionPrice > 0 && cleanedPortionPrices.includes(p.portionPrice)) {
      if (!unitTypeGroups[p.unittype]) {
        unitTypeGroups[p.unittype] = [];
      }
      unitTypeGroups[p.unittype].push(p);
    }
  });

  // Find the largest unit type group
  let largestGroup = [];
  let largestUnitType = '';
  Object.entries(unitTypeGroups).forEach(([unitType, products]) => {
    if (products.length > largestGroup.length) {
      largestGroup = products;
      largestUnitType = unitType;
    }
  });

  // Use portion price comparison if we have enough matching unit types
  if (largestGroup.length >= 2) {
    selectedPrices = largestGroup;
    comparisonMethod = `portion_price_${largestUnitType}`;
    const prices = largestGroup.map(p => p.portionPrice).sort((a, b) => a - b);
    const medianIndex = Math.floor(prices.length / 2);
    medianPrice = prices.length % 2 === 0
      ? (prices[medianIndex - 1] + prices[medianIndex]) / 2
      : prices[medianIndex];
  }
  // Step 4: Fallback comparison method - regular prices
  else if (cleanedRegularPrices.length >= 2) {
    selectedPrices = allProductPrices.filter(p =>
      p.regularPrice > 0 && cleanedRegularPrices.includes(p.regularPrice)
    );
    comparisonMethod = 'regular_price';
    const prices = cleanedRegularPrices.sort((a, b) => a - b);
    const medianIndex = Math.floor(prices.length / 2);
    medianPrice = prices.length % 2 === 0
      ? (prices[medianIndex - 1] + prices[medianIndex]) / 2
      : prices[medianIndex];
  }

  // Step 5: Analyze each product and determine proximity
  const productResults = [];
  let cheapestProduct = null;
  let cheapestPrice = Infinity;
  let maxVariance = 0;

  // Group results by original product (matchKey)
  const productGroups = {};
  selectedPrices.forEach(priceData => {
    const baseMatchKey = priceData.matchKey.replace(/_[^_]+$/, ''); // Remove price type suffix
    if (!productGroups[baseMatchKey]) {
      productGroups[baseMatchKey] = [];
    }
    productGroups[baseMatchKey].push(priceData);
  });

  Object.entries(productGroups).forEach(([baseMatchKey, priceOptions]) => {
    // Select best price option for this product
    let bestOption = priceOptions[0];
    let comparisonPrice = comparisonMethod.startsWith('portion_price')
      ? bestOption.portionPrice
      : bestOption.regularPrice;

    // For multiple options, choose the one closest to median
    if (priceOptions.length > 1) {
      let minVariance = Infinity;
      priceOptions.forEach(option => {
        const price = comparisonMethod.startsWith('portion_price')
          ? option.portionPrice
          : option.regularPrice;
        const variance = Math.abs(price - medianPrice) / medianPrice * 100;
        if (variance < minVariance) {
          minVariance = variance;
          bestOption = option;
          comparisonPrice = price;
        }
      });
    }

    // Calculate variance from median
    const varianceFromMedian = Math.abs(comparisonPrice - medianPrice) / medianPrice * 100;

    // Determine proximity status
    const proximityStatus = varianceFromMedian <= 40 ? 'close' : 'not close';

    // Track cheapest among 'close' products
    if (proximityStatus === 'close' && comparisonPrice < cheapestPrice) {
      cheapestPrice = comparisonPrice;
      cheapestProduct = bestOption.matchKey;
    }

    maxVariance = Math.max(maxVariance, varianceFromMedian);

    productResults.push({
      matchKey: bestOption.matchKey,
      proximityStatus,
      varianceFromMedian,
      isCheapest: false,
      selectedPriceOption: bestOption,
      comparisonPrice: comparisonPrice,
      allOptions: priceOptions
    });
  });

  // Mark the cheapest product
  if (cheapestProduct) {
    const cheapestResult = productResults.find(r => r.matchKey === cheapestProduct);
    if (cheapestResult) {
      cheapestResult.isCheapest = true;
    }
  }

  // Determine group status
  const closeProducts = productResults.filter(r => r.proximityStatus === 'close');
  const groupStatus = closeProducts.length >= 2 ? 'close' : 'not close';

  return {
    productResults,
    medianPrice,
    maxVariance,
    groupStatus,
    cheapestProductKey: cheapestProduct,
    outlierCount: (portionPrices.length - cleanedPortionPrices.length) +
                  (regularPrices.length - cleanedRegularPrices.length),
    comparisonMethod,
    totalPriceOptions: allProductPrices.length
  };
}

async function analyzePriceComparisons() {
  console.log('Starting price comparison analysis...');

  // Load data
  console.log('Loading seller data...');
  const sellerData = await loadSellerData();

  console.log('Loading matches.json...');
  const matchesData = await fs.readFile('matches.json', 'utf8');
  const matches = JSON.parse(matchesData);

  console.log(`Processing ${matches.length} product groups...`);

  const results = {
    productGroups: [],
    summary: {
      totalGroups: matches.length,
      processedGroups: 0,
      groupsWithPrices: 0,
      closeMatches: 0,
      notCloseMatches: 0,
      averagePriceVariance: 0
    }
  };

  let totalVariance = 0;
  let groupsWithVariance = 0;

  for (let i = 0; i < matches.length; i++) {
    const group = matches[i];
    const groupId = `group_${i + 1}`;

    if (i % 1000 === 0) {
      console.log(`Processing group ${i + 1}/${matches.length}...`);
    }

    const productGroup = {
      groupId,
      products: [],
      groupAnalysis: {
        hasValidPrices: false,
        cheapestProduct: null,
        maxPriceVariance: 0,
        groupStatus: 'not close'
      }
    };

    const validProducts = [];

    // Process each product in the group
    for (const [matchKey, productName] of Object.entries(group)) {
      const parsed = parseMatchKey(matchKey);
      if (!parsed) continue;

      const { seller, productNumber } = parsed;
      const product = sellerData[seller]?.[productNumber];

      if (!product) continue;

      // Calculate all available prices using comprehensive analysis
      const allPricesCalc = calculateAllPrices(product, seller);
      if (!allPricesCalc) continue; // Skip products with no valid prices

      // Extract all price properties for display
      const allPriceProperties = extractPriceProperties(product, seller);

      const productAnalysis = {
        originalProductData: {
          ...product,
          seller,
          matchKey,
          productName
        },
        priceAnalysis: {
          // Keep backward compatibility with single price fields
          regularPrice: allPricesCalc.priceOptions[0]?.price || 0,
          portionPrice: allPricesCalc.priceOptions[0]?.portionPrice || 0,
          unittype: allPricesCalc.priceOptions[0]?.unittype || product.unittype || '',
          quantity: allPricesCalc.priceOptions[0]?.quantity || parseFloat(product.quantity) || 1,

          // New comprehensive price data
          allPrices: allPricesCalc,
          allPriceProperties: allPriceProperties,

          // Analysis results (will be filled by analyzeGroupPrices)
          isCheapest: false,
          priceStatus: 'close',
          priceVariancePercentage: 0,
          selectedPriceProperty: null,
          comparisonMethod: null
        }
      };

      validProducts.push(productAnalysis);
      productGroup.products.push(productAnalysis);
    }

    // Analyze price relationships within the group
    if (validProducts.length > 1) {
      productGroup.groupAnalysis.hasValidPrices = true;
      results.summary.groupsWithPrices++;

      // Enhanced price analysis with outlier removal and median-based proximity
      const analysisResult = analyzeGroupPrices(validProducts);

      // Apply analysis results to products
      validProducts.forEach(product => {
        const productResult = analysisResult.productResults.find(
          r => r.matchKey === product.originalProductData.matchKey
        );
        if (productResult) {
          product.priceAnalysis.priceStatus = productResult.proximityStatus;
          product.priceAnalysis.priceVariancePercentage = productResult.varianceFromMedian;
          product.priceAnalysis.isCheapest = productResult.isCheapest;
          product.priceAnalysis.selectedPriceProperty = productResult.selectedPriceOption?.priceProperty;
          product.priceAnalysis.comparisonMethod = analysisResult.comparisonMethod;
          product.priceAnalysis.comparisonPrice = productResult.comparisonPrice;
          product.priceAnalysis.selectedPriceOption = productResult.selectedPriceOption;
        }
      });

      productGroup.groupAnalysis.maxPriceVariance = analysisResult.maxVariance;
      productGroup.groupAnalysis.groupStatus = analysisResult.groupStatus;
      productGroup.groupAnalysis.cheapestProduct = analysisResult.cheapestProductKey;
      productGroup.groupAnalysis.medianPrice = analysisResult.medianPrice;
      productGroup.groupAnalysis.outlierCount = analysisResult.outlierCount;
      productGroup.groupAnalysis.comparisonMethod = analysisResult.comparisonMethod;
      productGroup.groupAnalysis.totalPriceOptions = analysisResult.totalPriceOptions;

      if (analysisResult.groupStatus === 'close') {
        results.summary.closeMatches++;
      } else {
        results.summary.notCloseMatches++;
      }

      totalVariance += analysisResult.maxVariance;
      groupsWithVariance++;
    }

    results.productGroups.push(productGroup);
    results.summary.processedGroups++;
  }

  // Calculate summary statistics
  if (groupsWithVariance > 0) {
    results.summary.averagePriceVariance = totalVariance / groupsWithVariance;
  }

  console.log('\nAnalysis Summary:');
  console.log(`Total groups: ${results.summary.totalGroups}`);
  console.log(`Groups with valid prices: ${results.summary.groupsWithPrices}`);
  console.log(`Close matches: ${results.summary.closeMatches}`);
  console.log(`Not close matches: ${results.summary.notCloseMatches}`);
  console.log(`Average price variance: ${results.summary.averagePriceVariance.toFixed(2)}%`);

  // Create a more manageable output by filtering and limiting data
  const filteredResults = {
    productGroups: results.productGroups
      .filter(group => group.groupAnalysis.hasValidPrices && group.products.length > 1)
      .slice(0, 1000) // Limit to first 1000 groups for web app performance
      .map(group => ({
        ...group,
        products: group.products.map(product => ({
          originalProductData: {
            seller: product.originalProductData.seller,
            matchKey: product.originalProductData.matchKey,
            productName: product.originalProductData.productName,
            productnumber: product.originalProductData.productnumber,
            name: product.originalProductData.name,
            packsize: product.originalProductData.packsize || product.originalProductData['Pack Size']
          },
          priceAnalysis: {
            regularPrice: product.priceAnalysis.regularPrice,
            portionPrice: product.priceAnalysis.portionPrice,
            unittype: product.priceAnalysis.unittype,
            quantity: product.priceAnalysis.quantity,
            isCheapest: product.priceAnalysis.isCheapest,
            priceStatus: product.priceAnalysis.priceStatus,
            priceVariancePercentage: product.priceAnalysis.priceVariancePercentage,
            // Include comprehensive price data for UI display
            allPriceProperties: product.priceAnalysis.allPriceProperties,
            selectedPriceProperty: product.priceAnalysis.selectedPriceProperty,
            comparisonMethod: product.priceAnalysis.comparisonMethod,
            comparisonPrice: product.priceAnalysis.comparisonPrice
          }
        }))
      })),
    summary: results.summary
  };

  // Save results
  console.log('\nSaving results to price_comparison_results.json...');
  await fs.writeFile('price_comparison_results.json', JSON.stringify(filteredResults, null, 2));

  // Also save a summary report
  const summaryReport = {
    analysisDate: new Date().toISOString(),
    summary: results.summary,
    topCheapestProducts: results.productGroups
      .filter(group => group.groupAnalysis.hasValidPrices)
      .map(group => {
        const cheapest = group.products.find(p => p.priceAnalysis.isCheapest);
        return cheapest ? {
          groupId: group.groupId,
          productName: cheapest.originalProductData.productName,
          seller: cheapest.originalProductData.seller,
          portionPrice: cheapest.priceAnalysis.portionPrice,
          regularPrice: cheapest.priceAnalysis.regularPrice,
          groupVariance: group.groupAnalysis.maxPriceVariance
        } : null;
      })
      .filter(item => item !== null)
      .sort((a, b) => a.portionPrice - b.portionPrice)
      .slice(0, 100)
  };

  await fs.writeFile('price_analysis_summary.json', JSON.stringify(summaryReport, null, 2));

  console.log('Price comparison analysis completed!');
  console.log(`Saved ${filteredResults.productGroups.length} product groups to web app data file`);
  return results;
}

// Run the analysis
if (require.main === module) {
  analyzePriceComparisons().catch(console.error);
}

module.exports = { analyzePriceComparisons };

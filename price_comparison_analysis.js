const fs = require('fs').promises;
const {
  loadSellerData,
  extractPriceProperties,
  calculatePrice,
  calculatePriceDifference,
  parseMatchKey,
  removeOutliers
} = require('./price_analysis');

// Enhanced price analysis with median-based proximity and outlier removal
function analyzeGroupPrices(validProducts) {
  // Collect all prices for analysis
  const regularPrices = [];
  const portionPrices = [];
  const productPriceMap = new Map();

  validProducts.forEach(product => {
    const regularPrice = product.priceAnalysis.regularPrice;
    const portionPrice = product.priceAnalysis.portionPrice;
    const matchKey = product.originalProductData.matchKey;

    // Prefer portion price, fall back to regular price
    const comparisonPrice = portionPrice > 0 ? portionPrice : regularPrice;

    if (comparisonPrice > 0) {
      productPriceMap.set(matchKey, {
        price: comparisonPrice,
        product: product,
        unittype: product.priceAnalysis.unittype
      });

      if (regularPrice > 0) regularPrices.push(regularPrice);
      if (portionPrice > 0) portionPrices.push(portionPrice);
    }
  });

  // Remove outliers from price arrays
  const cleanedRegularPrices = removeOutliers(regularPrices);
  const cleanedPortionPrices = removeOutliers(portionPrices);

  // Determine which price type to use for comparison
  let comparisonPrices = [];
  let usePortionPrices = false;

  // Prioritize portion prices if we have unit type consistency
  if (cleanedPortionPrices.length > 1) {
    const unitTypes = Array.from(productPriceMap.values())
      .filter(p => p.product.priceAnalysis.portionPrice > 0)
      .map(p => p.unittype);

    const uniqueUnitTypes = [...new Set(unitTypes)];

    // If most products have the same unit type, use portion prices
    if (uniqueUnitTypes.length <= 2 || unitTypes.length >= cleanedRegularPrices.length) {
      comparisonPrices = cleanedPortionPrices;
      usePortionPrices = true;
    }
  }

  // Fall back to regular prices if portion prices aren't suitable
  if (comparisonPrices.length === 0 && cleanedRegularPrices.length > 1) {
    comparisonPrices = cleanedRegularPrices;
    usePortionPrices = false;
  }

  // Calculate median price
  const sortedPrices = [...comparisonPrices].sort((a, b) => a - b);
  const medianIndex = Math.floor(sortedPrices.length / 2);
  const medianPrice = sortedPrices.length % 2 === 0
    ? (sortedPrices[medianIndex - 1] + sortedPrices[medianIndex]) / 2
    : sortedPrices[medianIndex];

  // Analyze each product
  const productResults = [];
  let cheapestProduct = null;
  let cheapestPrice = Infinity;
  let maxVariance = 0;

  productPriceMap.forEach((priceData, matchKey) => {
    const comparisonPrice = usePortionPrices
      ? priceData.product.priceAnalysis.portionPrice
      : priceData.product.priceAnalysis.regularPrice;

    if (comparisonPrice <= 0) return;

    // Calculate variance from median
    const varianceFromMedian = Math.abs(comparisonPrice - medianPrice) / medianPrice * 100;

    // Determine proximity status based on 40% threshold from median
    const proximityStatus = varianceFromMedian <= 40 ? 'close' : 'not close';

    // Track cheapest among 'close' products
    if (proximityStatus === 'close' && comparisonPrice < cheapestPrice) {
      cheapestPrice = comparisonPrice;
      cheapestProduct = matchKey;
    }

    maxVariance = Math.max(maxVariance, varianceFromMedian);

    productResults.push({
      matchKey,
      proximityStatus,
      varianceFromMedian,
      isCheapest: false // Will be set below
    });
  });

  // Mark the cheapest product
  if (cheapestProduct) {
    const cheapestResult = productResults.find(r => r.matchKey === cheapestProduct);
    if (cheapestResult) {
      cheapestResult.isCheapest = true;
    }
  }

  // Determine group status
  const closeProducts = productResults.filter(r => r.proximityStatus === 'close');
  const groupStatus = closeProducts.length >= 2 ? 'close' : 'not close';

  return {
    productResults,
    medianPrice,
    maxVariance,
    groupStatus,
    cheapestProductKey: cheapestProduct,
    outlierCount: (regularPrices.length - cleanedRegularPrices.length) +
                  (portionPrices.length - cleanedPortionPrices.length)
  };
}

async function analyzePriceComparisons() {
  console.log('Starting price comparison analysis...');

  // Load data
  console.log('Loading seller data...');
  const sellerData = await loadSellerData();

  console.log('Loading matches.json...');
  const matchesData = await fs.readFile('matches.json', 'utf8');
  const matches = JSON.parse(matchesData);

  console.log(`Processing ${matches.length} product groups...`);

  const results = {
    productGroups: [],
    summary: {
      totalGroups: matches.length,
      processedGroups: 0,
      groupsWithPrices: 0,
      closeMatches: 0,
      notCloseMatches: 0,
      averagePriceVariance: 0
    }
  };

  let totalVariance = 0;
  let groupsWithVariance = 0;

  for (let i = 0; i < matches.length; i++) {
    const group = matches[i];
    const groupId = `group_${i + 1}`;

    if (i % 1000 === 0) {
      console.log(`Processing group ${i + 1}/${matches.length}...`);
    }

    const productGroup = {
      groupId,
      products: [],
      groupAnalysis: {
        hasValidPrices: false,
        cheapestProduct: null,
        maxPriceVariance: 0,
        groupStatus: 'not close'
      }
    };

    const validProducts = [];

    // Process each product in the group
    for (const [matchKey, productName] of Object.entries(group)) {
      const parsed = parseMatchKey(matchKey);
      if (!parsed) continue;

      const { seller, productNumber } = parsed;
      const product = sellerData[seller]?.[productNumber];

      if (!product) continue;

      // Calculate prices using enhanced rules
      const priceCalc = calculatePrice(product, seller);
      if (!priceCalc) continue; // Skip products with empty prices

      // Handle multiple price options for chef and sham suppliers
      if (priceCalc.multipleOptions) {
        // For suppliers with multiple price options, add each option as a separate product variant
        for (const option of priceCalc.multipleOptions) {
          const { priceProps, portionPriceProps } = extractPriceProperties(product);

          const productAnalysis = {
            originalProductData: {
              ...product,
              seller,
              matchKey: `${matchKey}_${option.priceType}`,
              productName,
              priceType: option.priceType
            },
            priceAnalysis: {
              regularPrice: option.price,
              portionPrice: option.portionPrice,
              unittype: option.unittype,
              quantity: option.quantity,
              allPriceProperties: { ...priceProps, ...portionPriceProps },
              isCheapest: false,
              priceStatus: 'close',
              priceVariancePercentage: 0,
              priceType: option.priceType
            }
          };

          validProducts.push(productAnalysis);
          productGroup.products.push(productAnalysis);
        }
      } else {
        // Standard single price handling
        const { priceProps, portionPriceProps } = extractPriceProperties(product);

        const productAnalysis = {
          originalProductData: {
            ...product,
            seller,
            matchKey,
            productName
          },
          priceAnalysis: {
            regularPrice: priceCalc.price,
            portionPrice: priceCalc.portionPrice,
            unittype: priceCalc.unittype,
            quantity: priceCalc.quantity,
            allPriceProperties: { ...priceProps, ...portionPriceProps },
            isCheapest: false,
            priceStatus: 'close',
            priceVariancePercentage: 0
          }
        };

        validProducts.push(productAnalysis);
        productGroup.products.push(productAnalysis);
      }
    }

    // Analyze price relationships within the group
    if (validProducts.length > 1) {
      productGroup.groupAnalysis.hasValidPrices = true;
      results.summary.groupsWithPrices++;

      // Enhanced price analysis with outlier removal and median-based proximity
      const analysisResult = analyzeGroupPrices(validProducts);

      // Apply analysis results to products
      validProducts.forEach(product => {
        const productResult = analysisResult.productResults.find(
          r => r.matchKey === product.originalProductData.matchKey
        );
        if (productResult) {
          product.priceAnalysis.priceStatus = productResult.proximityStatus;
          product.priceAnalysis.priceVariancePercentage = productResult.varianceFromMedian;
          product.priceAnalysis.isCheapest = productResult.isCheapest;
        }
      });

      productGroup.groupAnalysis.maxPriceVariance = analysisResult.maxVariance;
      productGroup.groupAnalysis.groupStatus = analysisResult.groupStatus;
      productGroup.groupAnalysis.cheapestProduct = analysisResult.cheapestProductKey;
      productGroup.groupAnalysis.medianPrice = analysisResult.medianPrice;
      productGroup.groupAnalysis.outlierCount = analysisResult.outlierCount;

      if (analysisResult.groupStatus === 'close') {
        results.summary.closeMatches++;
      } else {
        results.summary.notCloseMatches++;
      }

      totalVariance += analysisResult.maxVariance;
      groupsWithVariance++;
    }

    results.productGroups.push(productGroup);
    results.summary.processedGroups++;
  }

  // Calculate summary statistics
  if (groupsWithVariance > 0) {
    results.summary.averagePriceVariance = totalVariance / groupsWithVariance;
  }

  console.log('\nAnalysis Summary:');
  console.log(`Total groups: ${results.summary.totalGroups}`);
  console.log(`Groups with valid prices: ${results.summary.groupsWithPrices}`);
  console.log(`Close matches: ${results.summary.closeMatches}`);
  console.log(`Not close matches: ${results.summary.notCloseMatches}`);
  console.log(`Average price variance: ${results.summary.averagePriceVariance.toFixed(2)}%`);

  // Create a more manageable output by filtering and limiting data
  const filteredResults = {
    productGroups: results.productGroups
      .filter(group => group.groupAnalysis.hasValidPrices && group.products.length > 1)
      .slice(0, 1000) // Limit to first 1000 groups for web app performance
      .map(group => ({
        ...group,
        products: group.products.map(product => ({
          originalProductData: {
            seller: product.originalProductData.seller,
            matchKey: product.originalProductData.matchKey,
            productName: product.originalProductData.productName,
            productnumber: product.originalProductData.productnumber,
            name: product.originalProductData.name,
            packsize: product.originalProductData.packsize || product.originalProductData['Pack Size']
          },
          priceAnalysis: product.priceAnalysis
        }))
      })),
    summary: results.summary
  };

  // Save results
  console.log('\nSaving results to price_comparison_results.json...');
  await fs.writeFile('price_comparison_results.json', JSON.stringify(filteredResults, null, 2));

  // Also save a summary report
  const summaryReport = {
    analysisDate: new Date().toISOString(),
    summary: results.summary,
    topCheapestProducts: results.productGroups
      .filter(group => group.groupAnalysis.hasValidPrices)
      .map(group => {
        const cheapest = group.products.find(p => p.priceAnalysis.isCheapest);
        return cheapest ? {
          groupId: group.groupId,
          productName: cheapest.originalProductData.productName,
          seller: cheapest.originalProductData.seller,
          portionPrice: cheapest.priceAnalysis.portionPrice,
          regularPrice: cheapest.priceAnalysis.regularPrice,
          groupVariance: group.groupAnalysis.maxPriceVariance
        } : null;
      })
      .filter(item => item !== null)
      .sort((a, b) => a.portionPrice - b.portionPrice)
      .slice(0, 100)
  };

  await fs.writeFile('price_analysis_summary.json', JSON.stringify(summaryReport, null, 2));

  console.log('Price comparison analysis completed!');
  console.log(`Saved ${filteredResults.productGroups.length} product groups to web app data file`);
  return results;
}

// Run the analysis
if (require.main === module) {
  analyzePriceComparisons().catch(console.error);
}

module.exports = { analyzePriceComparisons };

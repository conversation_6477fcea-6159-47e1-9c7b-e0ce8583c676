const fs = require('fs').promises;
const {
  loadSellerData,
  extractPriceProperties,
  calculatePrice,
  calculateAllPrices,
  calculatePriceDifference,
  parseMatchKey,
  removeOutliers
} = require('./price_analysis');

// Simplified and fixed price analysis
function analyzeGroupPrices(validProducts) {
  if (validProducts.length === 0) {
    return {
      productResults: [],
      medianPrice: 0,
      maxVariance: 0,
      groupStatus: 'not close',
      cheapestProductKey: null,
      outlierCount: 0,
      comparisonMethod: 'none'
    };
  }

  // Step 1: Check if all products have the same unit type for portion price comparison
  const unitTypes = validProducts.map(p => p.priceAnalysis.unittype).filter(u => u);
  const uniqueUnitTypes = [...new Set(unitTypes)];
  const canUsePortionPrices = uniqueUnitTypes.length === 1 && uniqueUnitTypes[0];

  let comparisonMethod = 'regular_price';
  let comparisonPrices = [];
  let productPriceMap = new Map();

  if (canUsePortionPrices) {
    // Use portion prices when all products have same unit type
    comparisonMethod = `portion_price_${uniqueUnitTypes[0]}`;

    validProducts.forEach(product => {
      const portionPrice = product.priceAnalysis.portionPrice;
      if (portionPrice > 0) {
        comparisonPrices.push(portionPrice);
        productPriceMap.set(product.originalProductData.matchKey, {
          comparisonPrice: portionPrice,
          selectedProperty: 'portionprice', // Simplified - usually portionprice
          product: product
        });
      }
    });
  } else {
    // Fall back to regular prices
    validProducts.forEach(product => {
      const regularPrice = product.priceAnalysis.regularPrice;
      if (regularPrice > 0) {
        comparisonPrices.push(regularPrice);
        productPriceMap.set(product.originalProductData.matchKey, {
          comparisonPrice: regularPrice,
          selectedProperty: 'price', // Simplified - usually price
          product: product
        });
      }
    });
  }

  if (comparisonPrices.length === 0) {
    return {
      productResults: [],
      medianPrice: 0,
      maxVariance: 0,
      groupStatus: 'not close',
      cheapestProductKey: null,
      outlierCount: 0,
      comparisonMethod: 'none'
    };
  }

  // Step 2: Remove outliers and calculate median
  const cleanedPrices = removeOutliers(comparisonPrices);
  const sortedPrices = [...cleanedPrices].sort((a, b) => a - b);
  const medianIndex = Math.floor(sortedPrices.length / 2);
  const medianPrice = sortedPrices.length % 2 === 0
    ? (sortedPrices[medianIndex - 1] + sortedPrices[medianIndex]) / 2
    : sortedPrices[medianIndex];

  // Step 3: Analyze each product
  const productResults = [];
  let cheapestProduct = null;
  let cheapestPrice = Infinity;
  let maxVariance = 0;

  productPriceMap.forEach((priceData, matchKey) => {
    const comparisonPrice = priceData.comparisonPrice;

    // Only include products with cleaned prices (non-outliers)
    if (!cleanedPrices.includes(comparisonPrice)) {
      return; // Skip outliers
    }

    // Calculate variance from median
    const varianceFromMedian = Math.abs(comparisonPrice - medianPrice) / medianPrice * 100;

    // Determine proximity status
    const proximityStatus = varianceFromMedian <= 40 ? 'close' : 'not close';

    // Track cheapest among all valid products (not just close ones)
    if (comparisonPrice < cheapestPrice) {
      cheapestPrice = comparisonPrice;
      cheapestProduct = matchKey;
    }

    maxVariance = Math.max(maxVariance, varianceFromMedian);

    productResults.push({
      matchKey,
      proximityStatus,
      varianceFromMedian,
      isCheapest: false, // Will be set below
      comparisonPrice: comparisonPrice,
      selectedPriceProperty: priceData.selectedProperty
    });
  });

  // Mark the cheapest product
  if (cheapestProduct) {
    const cheapestResult = productResults.find(r => r.matchKey === cheapestProduct);
    if (cheapestResult) {
      cheapestResult.isCheapest = true;
    }
  }

  // Determine group status
  const closeProducts = productResults.filter(r => r.proximityStatus === 'close');
  const groupStatus = closeProducts.length >= 2 ? 'close' : 'not close';

  return {
    productResults,
    medianPrice,
    maxVariance,
    groupStatus,
    cheapestProductKey: cheapestProduct,
    outlierCount: comparisonPrices.length - cleanedPrices.length,
    comparisonMethod,
    totalPriceOptions: comparisonPrices.length
  };
}

async function analyzePriceComparisons() {
  console.log('Starting price comparison analysis...');

  // Load data
  console.log('Loading seller data...');
  const sellerData = await loadSellerData();

  console.log('Loading matches_cleaned.json...');
  const matchesData = await fs.readFile('matches_cleaned.json', 'utf8');
  const matches = JSON.parse(matchesData);

  console.log(`Processing ${matches.length} product groups...`);

  const results = {
    productGroups: [],
    summary: {
      totalGroups: matches.length,
      processedGroups: 0,
      groupsWithPrices: 0,
      closeMatches: 0,
      notCloseMatches: 0,
      averagePriceVariance: 0
    }
  };

  let totalVariance = 0;
  let groupsWithVariance = 0;

  for (let i = 0; i < matches.length; i++) {
    const group = matches[i];
    const groupId = `group_${i + 1}`;

    if (i % 1000 === 0) {
      console.log(`Processing group ${i + 1}/${matches.length}...`);
    }

    const productGroup = {
      groupId,
      products: [],
      groupAnalysis: {
        hasValidPrices: false,
        cheapestProduct: null,
        maxPriceVariance: 0,
        groupStatus: 'not close'
      }
    };

    const validProducts = [];

    // Process each product in the group
    for (const [matchKey, productName] of Object.entries(group)) {
      const parsed = parseMatchKey(matchKey);
      if (!parsed) continue;

      const { seller, productNumber } = parsed;
      const product = sellerData[seller]?.[productNumber];

      if (!product) continue;

      // Calculate all available prices using comprehensive analysis
      const allPricesCalc = calculateAllPrices(product, seller);
      if (!allPricesCalc) continue; // Skip products with no valid prices

      // Extract all price properties for display
      const allPriceProperties = extractPriceProperties(product, seller);

      const productAnalysis = {
        originalProductData: {
          ...product,
          seller,
          matchKey,
          productName
        },
        priceAnalysis: {
          // Keep backward compatibility with single price fields
          regularPrice: allPricesCalc.priceOptions[0]?.price || 0,
          portionPrice: allPricesCalc.priceOptions[0]?.portionPrice || 0,
          unittype: allPricesCalc.priceOptions[0]?.unittype || product.unittype || '',
          quantity: allPricesCalc.priceOptions[0]?.quantity || parseFloat(product.quantity) || 1,

          // New comprehensive price data
          allPrices: allPricesCalc,
          allPriceProperties: allPriceProperties,

          // Analysis results (will be filled by analyzeGroupPrices)
          isCheapest: false,
          priceStatus: 'close',
          priceVariancePercentage: 0,
          selectedPriceProperty: null,
          comparisonMethod: null
        }
      };

      validProducts.push(productAnalysis);
      productGroup.products.push(productAnalysis);
    }

    // Analyze price relationships within the group
    if (validProducts.length > 1) {
      productGroup.groupAnalysis.hasValidPrices = true;
      results.summary.groupsWithPrices++;

      // Enhanced price analysis with outlier removal and median-based proximity
      const analysisResult = analyzeGroupPrices(validProducts);

      // Apply analysis results to products
      validProducts.forEach(product => {
        const productResult = analysisResult.productResults.find(
          r => r.matchKey === product.originalProductData.matchKey
        );
        if (productResult) {
          product.priceAnalysis.priceStatus = productResult.proximityStatus;
          product.priceAnalysis.priceVariancePercentage = productResult.varianceFromMedian;
          product.priceAnalysis.isCheapest = productResult.isCheapest;
          product.priceAnalysis.selectedPriceProperty = productResult.selectedPriceProperty;
          product.priceAnalysis.comparisonMethod = analysisResult.comparisonMethod;
          product.priceAnalysis.comparisonPrice = productResult.comparisonPrice;
        } else {
          // For products not in results (outliers), set default values
          product.priceAnalysis.priceStatus = 'not close';
          product.priceAnalysis.priceVariancePercentage = 0;
          product.priceAnalysis.isCheapest = false;
          product.priceAnalysis.selectedPriceProperty = null;
          product.priceAnalysis.comparisonMethod = analysisResult.comparisonMethod;
          product.priceAnalysis.comparisonPrice = null;
        }
      });

      productGroup.groupAnalysis.maxPriceVariance = analysisResult.maxVariance;
      productGroup.groupAnalysis.groupStatus = analysisResult.groupStatus;
      productGroup.groupAnalysis.cheapestProduct = analysisResult.cheapestProductKey;
      productGroup.groupAnalysis.medianPrice = analysisResult.medianPrice;
      productGroup.groupAnalysis.outlierCount = analysisResult.outlierCount;
      productGroup.groupAnalysis.comparisonMethod = analysisResult.comparisonMethod;
      productGroup.groupAnalysis.totalPriceOptions = analysisResult.totalPriceOptions;

      if (analysisResult.groupStatus === 'close') {
        results.summary.closeMatches++;
      } else {
        results.summary.notCloseMatches++;
      }

      totalVariance += analysisResult.maxVariance;
      groupsWithVariance++;
    }

    results.productGroups.push(productGroup);
    results.summary.processedGroups++;
  }

  // Calculate summary statistics
  if (groupsWithVariance > 0) {
    results.summary.averagePriceVariance = totalVariance / groupsWithVariance;
  }

  console.log('\nAnalysis Summary:');
  console.log(`Total groups: ${results.summary.totalGroups}`);
  console.log(`Groups with valid prices: ${results.summary.groupsWithPrices}`);
  console.log(`Close matches: ${results.summary.closeMatches}`);
  console.log(`Not close matches: ${results.summary.notCloseMatches}`);
  console.log(`Average price variance: ${results.summary.averagePriceVariance.toFixed(2)}%`);

  // Create a more manageable output by filtering and limiting data
  const filteredResults = {
    productGroups: results.productGroups
      .filter(group => group.groupAnalysis.hasValidPrices && group.products.length > 1)
      .slice(0, 1000) // Limit to first 1000 groups for web app performance
      .map(group => ({
        ...group,
        products: group.products.map(product => ({
          originalProductData: {
            seller: product.originalProductData.seller,
            matchKey: product.originalProductData.matchKey,
            productName: product.originalProductData.productName,
            productnumber: product.originalProductData.productnumber,
            name: product.originalProductData.name,
            packsize: product.originalProductData.packsize || product.originalProductData['Pack Size']
          },
          priceAnalysis: {
            regularPrice: product.priceAnalysis.regularPrice,
            portionPrice: product.priceAnalysis.portionPrice,
            unittype: product.priceAnalysis.unittype,
            quantity: product.priceAnalysis.quantity,
            isCheapest: product.priceAnalysis.isCheapest,
            priceStatus: product.priceAnalysis.priceStatus,
            priceVariancePercentage: product.priceAnalysis.priceVariancePercentage,
            // Include comprehensive price data for UI display
            allPriceProperties: product.priceAnalysis.allPriceProperties,
            selectedPriceProperty: product.priceAnalysis.selectedPriceProperty,
            comparisonMethod: product.priceAnalysis.comparisonMethod,
            comparisonPrice: product.priceAnalysis.comparisonPrice
          }
        }))
      })),
    summary: results.summary
  };

  // Save results
  console.log('\nSaving results to price_comparison_results.json...');
  await fs.writeFile('price_comparison_results.json', JSON.stringify(filteredResults, null, 2));

  // Also save a summary report
  const summaryReport = {
    analysisDate: new Date().toISOString(),
    summary: results.summary,
    topCheapestProducts: results.productGroups
      .filter(group => group.groupAnalysis.hasValidPrices)
      .map(group => {
        const cheapest = group.products.find(p => p.priceAnalysis.isCheapest);
        return cheapest ? {
          groupId: group.groupId,
          productName: cheapest.originalProductData.productName,
          seller: cheapest.originalProductData.seller,
          portionPrice: cheapest.priceAnalysis.portionPrice,
          regularPrice: cheapest.priceAnalysis.regularPrice,
          groupVariance: group.groupAnalysis.maxPriceVariance
        } : null;
      })
      .filter(item => item !== null)
      .sort((a, b) => a.portionPrice - b.portionPrice)
      .slice(0, 100)
  };

  await fs.writeFile('price_analysis_summary.json', JSON.stringify(summaryReport, null, 2));

  console.log('Price comparison analysis completed!');
  console.log(`Saved ${filteredResults.productGroups.length} product groups to web app data file`);
  return results;
}

// Run the analysis
if (require.main === module) {
  analyzePriceComparisons().catch(console.error);
}

module.exports = { analyzePriceComparisons };
